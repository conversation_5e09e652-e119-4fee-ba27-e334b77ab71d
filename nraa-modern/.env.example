# NRAA OPMP Environment Configuration

# Database Configuration (using existing NRAA database)
DATABASE_URL="mysql://nraa:<EMAIL>:3306/nraa"
MYSQL_ROOT_PASSWORD=nraa_root_password
MYSQL_DATABASE=nraa
MYSQL_USER=nraa
MYSQL_PASSWORD=nraa

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_change_in_production

# Application Configuration
NODE_ENV=production
NEXT_PUBLIC_API_URL=http://localhost:3001

# Docker Registry
DOCKER_REGISTRY=tower.local:5000

# Optional: SSL Configuration
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/private.key
