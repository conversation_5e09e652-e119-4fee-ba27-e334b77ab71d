#!/bin/bash

# Script to configure <PERSON><PERSON> to use the local insecure registry
# This adds tower.local:5000 to <PERSON><PERSON>'s insecure registries list

set -e

REGISTRY="tower.local:5000"
DAEMON_JSON="/etc/docker/daemon.json"

echo "🔧 Configuring Docker to use insecure registry at $REGISTRY..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo "❌ This script must be run as root (sudo)"
  echo "Please run: sudo $0"
  exit 1
fi

# Create or update daemon.json
if [ -f "$DAEMON_JSON" ]; then
  echo "📄 Found existing $DAEMON_JSON"
  
  # Check if already configured
  if grep -q "\"$REGISTRY\"" "$DAEMON_JSON"; then
    echo "✅ Registry $REGISTRY is already configured as insecure"
    exit 0
  fi
  
  # Check if insecure-registries key exists
  if grep -q "\"insecure-registries\"" "$DAEMON_JSON"; then
    # Add to existing array
    echo "📝 Adding $REGISTRY to existing insecure-registries list"
    TMP_FILE=$(mktemp)
    cat "$DAEMON_JSON" | jq --arg reg "$REGISTRY" '.["insecure-registries"] += [$reg]' > "$TMP_FILE"
    cat "$TMP_FILE" > "$DAEMON_JSON"
    rm "$TMP_FILE"
  else
    # Add new key
    echo "📝 Adding insecure-registries key with $REGISTRY"
    TMP_FILE=$(mktemp)
    cat "$DAEMON_JSON" | jq --arg reg "$REGISTRY" '. + {"insecure-registries": [$reg]}' > "$TMP_FILE"
    cat "$TMP_FILE" > "$DAEMON_JSON"
    rm "$TMP_FILE"
  fi
else
  # Create new file
  echo "📝 Creating new $DAEMON_JSON with $REGISTRY as insecure"
  mkdir -p $(dirname "$DAEMON_JSON")
  echo "{\"insecure-registries\": [\"$REGISTRY\"]}" > "$DAEMON_JSON"
fi

echo "🔄 Restarting Docker daemon..."
systemctl restart docker

echo "⏳ Waiting for Docker to restart..."
sleep 5

echo "🔍 Verifying configuration..."
if docker info | grep -q "$REGISTRY"; then
  echo "✅ Docker successfully configured to use $REGISTRY as insecure registry"
else
  echo "⚠️  Registry not found in Docker info, but configuration was applied"
  echo "   Please check $DAEMON_JSON and restart Docker manually if needed"
fi

echo ""
echo "🎉 Configuration complete!"
echo "You can now run:"
echo "  ./test-registry.sh    # Test registry connectivity"
echo "  ./build.sh            # Build and push NRAA OPMP images"
