#!/bin/bash

# Test script to verify local registry connectivity
# This script tests if the local registry at tower.local:5000 is accessible

set -e

REGISTRY="tower.local:5000"

echo "🔍 Testing connectivity to local registry at $REGISTRY..."

# Test 1: Check if registry is reachable
echo "📡 Testing registry connectivity..."
if curl -f -s "http://$REGISTRY/v2/" > /dev/null; then
    echo "✅ Registry is reachable"
else
    echo "❌ Registry is not reachable at http://$REGISTRY/v2/"
    echo "Please ensure the registry is running and accessible"
    exit 1
fi

# Test 2: Try to pull a small test image
echo "📥 Testing image pull from registry..."
TEST_IMAGE="hello-world"
REGISTRY_IMAGE="$REGISTRY/$TEST_IMAGE"

# Pull official image and push to local registry for testing
echo "📦 Pulling $TEST_IMAGE from Docker Hub..."
docker pull $TEST_IMAGE

echo "🏷️  Tagging for local registry..."
docker tag $TEST_IMAGE $REGISTRY_IMAGE

echo "📤 Pushing to local registry..."
echo "ℹ️  Note: Using insecure registry (HTTP)"
if docker push $REGISTRY_IMAGE; then
    echo "✅ Successfully pushed test image to local registry"
else
    echo "❌ Failed to push to local registry"
    echo ""
    echo "🔧 To fix this, add tower.local:5000 to Docker's insecure registries:"
    echo "   1. Edit /etc/docker/daemon.json (or create it)"
    echo "   2. Add: {\"insecure-registries\": [\"tower.local:5000\"]}"
    echo "   3. Restart Docker: sudo systemctl restart docker"
    echo ""
    echo "Or run: sudo mkdir -p /etc/docker && echo '{\"insecure-registries\": [\"tower.local:5000\"]}' | sudo tee /etc/docker/daemon.json && sudo systemctl restart docker"
    exit 1
fi

# Test 3: Try to pull from local registry
echo "🧹 Removing local test image..."
docker rmi $REGISTRY_IMAGE $TEST_IMAGE || true

echo "📥 Pulling test image from local registry..."
if docker pull $REGISTRY_IMAGE; then
    echo "✅ Successfully pulled test image from local registry"
else
    echo "❌ Failed to pull from local registry"
    exit 1
fi

# Cleanup
echo "🧹 Cleaning up test image..."
docker rmi $REGISTRY_IMAGE || true

echo "🎉 Local registry test completed successfully!"
echo ""
echo "Your local registry at $REGISTRY is working correctly."
echo "You can now run:"
echo "  ./build.sh    # Build and push NRAA OPMP images"
echo "  ./deploy.sh   # Deploy the application"
