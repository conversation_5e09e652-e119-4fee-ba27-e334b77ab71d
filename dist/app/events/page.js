"use strict";
'use client';
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = EventsPage;
const react_1 = require("react");
const react_query_1 = require("@tanstack/react-query");
const lucide_react_1 = require("lucide-react");
const mockEvents = [
    {
        id: 1,
        name: "NSW State Championships 2024",
        start_date: Math.floor(new Date('2024-03-15').getTime() / 1000),
        end_date: Math.floor(new Date('2024-03-17').getTime() / 1000),
        location: "Sydney International Shooting Centre",
        associations: {
            name: "NSW Rifle Association"
        },
        _count: {
            matches: 12
        },
        is_competition: true
    },
    {
        id: 2,
        name: "Queen's Prize 2024",
        start_date: Math.floor(new Date('2024-04-20').getTime() / 1000),
        end_date: Math.floor(new Date('2024-04-21').getTime() / 1000),
        location: "Belmont Shooting Complex",
        associations: {
            name: "Queensland Rifle Association"
        },
        _count: {
            matches: 8
        },
        is_competition: true
    }
];
function EventsPage() {
    const [page, setPage] = (0, react_1.useState)(1);
    const [yearFilter, setYearFilter] = (0, react_1.useState)('2024');
    const { data, isLoading, error } = (0, react_query_1.useQuery)({
        queryKey: ['events', page, yearFilter],
        queryFn: async () => {
            await new Promise(resolve => setTimeout(resolve, 500));
            return {
                data: mockEvents,
                pagination: {
                    page: 1,
                    limit: 20,
                    total: 2,
                    pages: 1
                }
            };
        }
    });
    const formatDate = (timestamp) => {
        return new Date(timestamp * 1000).toLocaleDateString('en-AU', {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
        });
    };
    if (error) {
        return (<div className="px-4 py-8">
        <div className="text-red-600">Error loading events: {error.message}</div>
      </div>);
    }
    return (<div className="px-4 py-8">
      
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Events</h1>
          <p className="text-gray-600">Manage performance events and competitions</p>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center gap-2">
          <lucide_react_1.Plus className="h-4 w-4"/>
          Create Event
        </button>
      </div>

      
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex gap-2">
            <select value={yearFilter} onChange={(e) => setYearFilter(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="2024">2024</option>
              <option value="2023">2023</option>
              <option value="2022">2022</option>
            </select>
            <button className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
              Upcoming Only
            </button>
            <button className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
              Competitions Only
            </button>
          </div>
        </div>
      </div>

      
      {isLoading ? (<div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading events...</p>
        </div>) : (<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {data?.data.map((event) => (<div key={event.id} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {event.name}
                  </h3>
                  <div className="flex items-center text-sm text-gray-500 mb-2">
                    <lucide_react_1.Calendar className="h-4 w-4 mr-1"/>
                    {formatDate(event.start_date)} - {formatDate(event.end_date)}
                  </div>
                  <div className="flex items-center text-sm text-gray-500 mb-2">
                    <lucide_react_1.MapPin className="h-4 w-4 mr-1"/>
                    {event.location}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <lucide_react_1.Users className="h-4 w-4 mr-1"/>
                    {event.associations?.name}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-600">
                    {event._count.matches}
                  </div>
                  <div className="text-xs text-gray-500">Matches</div>
                </div>
              </div>
              
              <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                <div className="flex gap-2">
                  {event.is_competition && (<span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      Competition
                    </span>)}
                </div>
                <div className="flex gap-2">
                  <button className="text-blue-600 hover:text-blue-900 text-sm font-medium">
                    View Details
                  </button>
                  <button className="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                    Edit
                  </button>
                </div>
              </div>
            </div>))}
        </div>)}

      
      {data?.data.length === 0 && (<div className="text-center py-12">
          <lucide_react_1.Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4"/>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
          <p className="text-gray-500 mb-4">Get started by creating your first event.</p>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Create Event
          </button>
        </div>)}

      
      {data?.pagination && data.pagination.pages > 1 && (<div className="mt-6 flex justify-center">
          <div className="flex gap-2">
            <button onClick={() => setPage(page - 1)} disabled={page === 1} className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
              Previous
            </button>
            <span className="px-3 py-2 text-sm text-gray-700">
              Page {page} of {data.pagination.pages}
            </span>
            <button onClick={() => setPage(page + 1)} disabled={page === data.pagination.pages} className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
              Next
            </button>
          </div>
        </div>)}
    </div>);
}
//# sourceMappingURL=page.js.map