"use strict";
'use client';
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ShootersPage;
const react_1 = require("react");
const react_query_1 = require("@tanstack/react-query");
const lucide_react_1 = require("lucide-react");
const mockShooters = [
    {
        id: 1,
        sid: 12345,
        club: "Sydney Rifle Club",
        status: "Active",
        shooter_details: {
            first_name: "<PERSON>",
            last_name: "<PERSON>",
            email: "<EMAIL>",
            mobile_phone: "0412 345 678"
        },
        shooter_membership: {
            membership_no: "NSW001",
            associations: {
                name: "NSW Rifle Association"
            }
        }
    },
    {
        id: 2,
        sid: 12346,
        club: "Melbourne Shooting Club",
        status: "Active",
        shooter_details: {
            first_name: "<PERSON>",
            last_name: "<PERSON>",
            email: "<EMAIL>",
            mobile_phone: "0423 456 789"
        },
        shooter_membership: {
            membership_no: "VIC002",
            associations: {
                name: "Victorian Rifle Association"
            }
        }
    }
];
function ShootersPage() {
    const [page, setPage] = (0, react_1.useState)(1);
    const [search, setSearch] = (0, react_1.useState)('');
    const [statusFilter, setStatusFilter] = (0, react_1.useState)('');
    const { data, isLoading, error } = (0, react_query_1.useQuery)({
        queryKey: ['shooters', page, search, statusFilter],
        queryFn: async () => {
            await new Promise(resolve => setTimeout(resolve, 500));
            return {
                data: mockShooters,
                pagination: {
                    page: 1,
                    limit: 50,
                    total: 2,
                    pages: 1
                }
            };
        }
    });
    if (error) {
        return (<div className="px-4 py-8">
        <div className="text-red-600">Error loading shooters: {error.message}</div>
      </div>);
    }
    return (<div className="px-4 py-8">
      
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Shooters</h1>
          <p className="text-gray-600">Manage shooter profiles, memberships, and performance data</p>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center gap-2">
          <lucide_react_1.Plus className="h-4 w-4"/>
          Add Shooter
        </button>
      </div>

      
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <lucide_react_1.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"/>
            <input type="text" placeholder="Search by name, email, or shooter ID..." value={search} onChange={(e) => setSearch(e.target.value)} className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"/>
          </div>
          <div className="flex gap-2">
            <select value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">All Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Deceased">Deceased</option>
              <option value="Honorary">Honorary</option>
            </select>
            <button className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center gap-2">
              <lucide_react_1.Filter className="h-4 w-4"/>
              More Filters
            </button>
          </div>
        </div>
      </div>

      
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        {isLoading ? (<div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">Loading shooters...</p>
          </div>) : (<div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    SID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Club
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Association
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data?.data.map((shooter) => (<tr key={shooter.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {shooter.sid}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {shooter.shooter_details?.first_name} {shooter.shooter_details?.last_name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {shooter.shooter_details?.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {shooter.club}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {shooter.shooter_membership?.associations?.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${shooter.status === 'Active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'}`}>
                        {shooter.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900 mr-3">
                        View
                      </button>
                      <button className="text-indigo-600 hover:text-indigo-900">
                        Edit
                      </button>
                    </td>
                  </tr>))}
              </tbody>
            </table>
          </div>)}
      </div>

      
      {data?.pagination && data.pagination.pages > 1 && (<div className="mt-6 flex justify-between items-center">
          <div className="text-sm text-gray-700">
            Showing {((page - 1) * data.pagination.limit) + 1} to {Math.min(page * data.pagination.limit, data.pagination.total)} of {data.pagination.total} results
          </div>
          <div className="flex gap-2">
            <button onClick={() => setPage(page - 1)} disabled={page === 1} className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
              Previous
            </button>
            <button onClick={() => setPage(page + 1)} disabled={page === data.pagination.pages} className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
              Next
            </button>
          </div>
        </div>)}
    </div>);
}
//# sourceMappingURL=page.js.map